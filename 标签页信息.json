{"scan_time": "2025-08-18 11:37:11", "total_tabs": 7, "tabs": [{"index": 1, "tab_id": "AA3F8566C54E0A7809391FAA67D654A8", "title": "商品列表", "url": "https://agentseller.temu.com/goods/list", "is_active": false, "is_loading": false, "ready_state": "complete"}, {"index": 2, "tab_id": "D30BC9865B79480390667D2D49BC1763", "title": "Offscreen", "url": "chrome-extension://ndcooeababalnlpkfedmmbbbgkljhpjf/src/offscreen.html", "is_active": false, "is_loading": false, "ready_state": "complete"}, {"index": 3, "tab_id": "59CE4A9645577DC54E2C74FEC1B48B5C", "title": "商品列表", "url": "https://agentseller.temu.com/goods/list", "is_active": false, "is_loading": false, "ready_state": "complete"}, {"index": 4, "tab_id": "4F8CB6526A67C663894DE521FB5CA422", "title": "商品列表", "url": "https://agentseller.temu.com/goods/list", "is_active": false, "is_loading": false, "ready_state": "complete"}, {"index": 5, "tab_id": "F8219E0787A45BBE0D9A26FD71524CA7", "title": "店小秘--订单管理", "url": "https://www.dianxiaomi.com/order/index.htm", "is_active": false, "is_loading": false, "ready_state": "complete"}, {"index": 6, "tab_id": "04366DE327324616E5749C2577BAD5CB", "title": "店小秘--添加Temu产品", "url": "https://www.dianxiaomi.com/web/temu/add#skuInfo", "is_active": false, "is_loading": false, "ready_state": "complete"}, {"index": 7, "tab_id": "FF65DC34C3C564C2A69E73D2259380C7", "title": "店小秘--产品管理", "url": "https://www.dianxiaomi.com/web/temu/choiceTemuList/online", "is_active": false, "is_loading": false, "ready_state": "complete"}]}